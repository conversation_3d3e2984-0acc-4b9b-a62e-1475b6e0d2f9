import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:io';
import 'failures.dart';
import 'error_handler.dart';
import '../database/database_manager.dart';

/// 增强错误处理管理器
/// 
/// 在现有错误处理基础上提供错误记录、统计和报告功能
/// **功能依赖**: 需要启用 error_handling 模块
/// **配置项**: FEATURE_ERROR_HANDLING
@singleton
class EnhancedErrorHandler {
  EnhancedErrorHandler(this._databaseManager);

  final DatabaseManager _databaseManager;
  
  static const String _errorLogKey = 'error_logs';
  static const int _maxErrorLogs = 1000;
  
  final StreamController<ErrorEvent> _errorEventController = 
      StreamController<ErrorEvent>.broadcast();

  /// 错误事件流
  Stream<ErrorEvent> get errorEvents => _errorEventController.stream;

  /// 处理错误并记录
  Future<Failure> handleAndLogError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? metadata,
    ErrorSeverity severity = ErrorSeverity.error,
    bool shouldReport = true,
  }) async {
    // 使用现有的错误处理器转换错误
    final failure = ErrorHandler.handleException(error);
    
    try {
      final errorInfo = _createErrorInfo(
        error,
        failure: failure,
        stackTrace: stackTrace,
        context: context,
        metadata: metadata,
        severity: severity,
      );

      // 记录错误
      await _logError(errorInfo);

      // 发送错误事件
      _errorEventController.add(ErrorEvent(errorInfo));

      // 报告错误（如果需要）
      if (shouldReport && !kDebugMode) {
        await _reportError(errorInfo);
      }

      // 在调试模式下打印错误
      if (kDebugMode) {
        _printError(errorInfo);
      }
    } catch (e) {
      // 错误处理器本身出错时的兜底处理
      print('Error in EnhancedErrorHandler: $e');
      ErrorHandler.logError(error, stackTrace);
    }

    return failure;
  }

  /// 处理业务错误
  Future<void> handleFailure(
    Failure failure, {
    String? context,
    Map<String, dynamic>? metadata,
    bool shouldReport = false,
  }) async {
    final severity = _mapFailureToSeverity(failure);
    
    await handleAndLogError(
      failure,
      context: context,
      metadata: metadata,
      severity: severity,
      shouldReport: shouldReport,
    );
  }

  /// 处理未捕获的异常
  Future<void> handleUncaughtError(
    dynamic error,
    StackTrace stackTrace,
  ) async {
    await handleAndLogError(
      error,
      stackTrace: stackTrace,
      context: 'Uncaught Exception',
      severity: ErrorSeverity.fatal,
      shouldReport: true,
    );
  }

  /// 获取错误日志
  Future<List<ErrorInfo>> getErrorLogs({
    int? limit,
    ErrorSeverity? minSeverity,
    DateTime? since,
  }) async {
    try {
      final defaultBox = _databaseManager.defaultBox;
      final logs = defaultBox.get(_errorLogKey, defaultValue: <dynamic>[]) as List;
      
      var errorLogs = logs
          .map((log) => ErrorInfo.fromJson(log as Map<String, dynamic>))
          .toList();

      // 按时间倒序排列
      errorLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // 应用过滤条件
      if (minSeverity != null) {
        errorLogs = errorLogs
            .where((log) => log.severity.index >= minSeverity.index)
            .toList();
      }

      if (since != null) {
        errorLogs = errorLogs
            .where((log) => log.timestamp.isAfter(since))
            .toList();
      }

      if (limit != null && limit > 0) {
        errorLogs = errorLogs.take(limit).toList();
      }

      return errorLogs;
    } catch (e) {
      print('Failed to get error logs: $e');
      return [];
    }
  }

  /// 清除错误日志
  Future<void> clearErrorLogs() async {
    try {
      final defaultBox = _databaseManager.defaultBox;
      await defaultBox.delete(_errorLogKey);
    } catch (e) {
      print('Failed to clear error logs: $e');
    }
  }

  /// 获取错误统计信息
  Future<ErrorStats> getErrorStats() async {
    try {
      final logs = await getErrorLogs();
      final now = DateTime.now();
      final last24Hours = now.subtract(const Duration(hours: 24));
      final last7Days = now.subtract(const Duration(days: 7));

      final severityCount = <ErrorSeverity, int>{};
      final typeCount = <String, int>{};
      int last24HoursCount = 0;
      int last7DaysCount = 0;

      for (final log in logs) {
        // 统计严重程度
        severityCount[log.severity] = (severityCount[log.severity] ?? 0) + 1;
        
        // 统计错误类型
        final errorType = log.errorType ?? 'Unknown';
        typeCount[errorType] = (typeCount[errorType] ?? 0) + 1;

        // 统计时间范围
        if (log.timestamp.isAfter(last24Hours)) {
          last24HoursCount++;
        }
        if (log.timestamp.isAfter(last7Days)) {
          last7DaysCount++;
        }
      }

      return ErrorStats(
        totalErrors: logs.length,
        last24HoursCount: last24HoursCount,
        last7DaysCount: last7DaysCount,
        severityCount: severityCount,
        typeCount: typeCount,
      );
    } catch (e) {
      print('Failed to get error stats: $e');
      return const ErrorStats(
        totalErrors: 0,
        last24HoursCount: 0,
        last7DaysCount: 0,
        severityCount: {},
        typeCount: {},
      );
    }
  }

  /// 创建错误信息
  ErrorInfo _createErrorInfo(
    dynamic error, {
    required Failure failure,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? metadata,
    ErrorSeverity severity = ErrorSeverity.error,
  }) {
    String message = failure.message;
    String? errorType = error?.runtimeType.toString();
    String? code = failure.code;

    return ErrorInfo(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      message: message,
      errorType: errorType,
      code: code,
      stackTrace: stackTrace?.toString(),
      context: context,
      metadata: metadata ?? {},
      severity: severity,
      timestamp: DateTime.now(),
      platform: Platform.operatingSystem,
      appVersion: '1.0.0', // 应该从配置中获取
      failureType: failure.runtimeType.toString(),
    );
  }

  /// 记录错误到本地
  Future<void> _logError(ErrorInfo errorInfo) async {
    try {
      final defaultBox = _databaseManager.defaultBox;
      final logs = defaultBox.get(_errorLogKey, defaultValue: <dynamic>[]) as List;
      
      // 添加新的错误日志
      logs.add(errorInfo.toJson());

      // 限制日志数量
      if (logs.length > _maxErrorLogs) {
        logs.removeRange(0, logs.length - _maxErrorLogs);
      }

      await defaultBox.put(_errorLogKey, logs);
    } catch (e) {
      print('Failed to log error: $e');
    }
  }

  /// 报告错误到远程服务
  Future<void> _reportError(ErrorInfo errorInfo) async {
    try {
      // 这里可以集成第三方错误报告服务
      // 例如：Sentry, Crashlytics, Bugsnag等
      print('Error reported: ${errorInfo.message}');
    } catch (e) {
      print('Failed to report error: $e');
    }
  }

  /// 打印错误信息
  void _printError(ErrorInfo errorInfo) {
    final severityIcon = _getSeverityIcon(errorInfo.severity);
    print('$severityIcon ${errorInfo.severity.name.toUpperCase()}: ${errorInfo.message}');
    
    if (errorInfo.context != null) {
      print('Context: ${errorInfo.context}');
    }
    
    if (errorInfo.code != null) {
      print('Code: ${errorInfo.code}');
    }
    
    if (errorInfo.stackTrace != null) {
      print('Stack trace:\n${errorInfo.stackTrace}');
    }
    
    if (errorInfo.metadata.isNotEmpty) {
      print('Metadata: ${errorInfo.metadata}');
    }
  }

  /// 获取严重程度图标
  String _getSeverityIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.debug:
        return '🐛';
      case ErrorSeverity.info:
        return 'ℹ️';
      case ErrorSeverity.warning:
        return '⚠️';
      case ErrorSeverity.error:
        return '❌';
      case ErrorSeverity.fatal:
        return '💀';
    }
  }

  /// 将业务错误映射为严重程度
  ErrorSeverity _mapFailureToSeverity(Failure failure) {
    if (failure is ValidationFailure) {
      return ErrorSeverity.warning;
    } else if (failure is NetworkFailure) {
      return ErrorSeverity.error;
    } else if (failure is AuthenticationFailure || failure is AuthorizationFailure) {
      return ErrorSeverity.error;
    } else if (failure is ServerFailure) {
      return ErrorSeverity.error;
    } else if (failure is FeatureDisabledFailure) {
      return ErrorSeverity.info;
    } else {
      return ErrorSeverity.error;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await _errorEventController.close();
  }
}

/// 错误信息
class ErrorInfo {
  const ErrorInfo({
    required this.id,
    required this.message,
    this.errorType,
    this.code,
    this.stackTrace,
    this.context,
    required this.metadata,
    required this.severity,
    required this.timestamp,
    required this.platform,
    required this.appVersion,
    this.failureType,
  });

  final String id;
  final String message;
  final String? errorType;
  final String? code;
  final String? stackTrace;
  final String? context;
  final Map<String, dynamic> metadata;
  final ErrorSeverity severity;
  final DateTime timestamp;
  final String platform;
  final String appVersion;
  final String? failureType;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
      'error_type': errorType,
      'code': code,
      'stack_trace': stackTrace,
      'context': context,
      'metadata': metadata,
      'severity': severity.index,
      'timestamp': timestamp.toIso8601String(),
      'platform': platform,
      'app_version': appVersion,
      'failure_type': failureType,
    };
  }

  factory ErrorInfo.fromJson(Map<String, dynamic> json) {
    return ErrorInfo(
      id: json['id'] as String,
      message: json['message'] as String,
      errorType: json['error_type'] as String?,
      code: json['code'] as String?,
      stackTrace: json['stack_trace'] as String?,
      context: json['context'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      severity: ErrorSeverity.values[json['severity'] as int],
      timestamp: DateTime.parse(json['timestamp'] as String),
      platform: json['platform'] as String,
      appVersion: json['app_version'] as String,
      failureType: json['failure_type'] as String?,
    );
  }

  @override
  String toString() {
    return 'ErrorInfo(id: $id, message: $message, severity: $severity)';
  }
}

/// 错误严重程度
enum ErrorSeverity {
  debug,
  info,
  warning,
  error,
  fatal,
}

/// 错误事件
class ErrorEvent {
  const ErrorEvent(this.errorInfo);
  final ErrorInfo errorInfo;
}

/// 错误统计信息
class ErrorStats {
  const ErrorStats({
    required this.totalErrors,
    required this.last24HoursCount,
    required this.last7DaysCount,
    required this.severityCount,
    required this.typeCount,
  });

  final int totalErrors;
  final int last24HoursCount;
  final int last7DaysCount;
  final Map<ErrorSeverity, int> severityCount;
  final Map<String, int> typeCount;

  @override
  String toString() {
    return 'ErrorStats(total: $totalErrors, 24h: $last24HoursCount, 7d: $last7DaysCount)';
  }
}
