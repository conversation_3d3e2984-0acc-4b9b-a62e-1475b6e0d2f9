import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../errors/failures.dart';

/// 错误处理拦截器
/// 
/// 统一处理网络请求错误，转换为业务错误
/// **功能依赖**: 需要启用 network 模块
/// **配置项**: FEATURE_NETWORK
@injectable
class ErrorInterceptor extends Interceptor {
  ErrorInterceptor();

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) {
    // 记录错误信息
    _logError(err);

    // 转换为业务错误
    final failure = _mapDioExceptionToFailure(err);
    
    // 创建新的DioException，包含业务错误信息
    final businessError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: failure,
      message: failure.message,
    );

    handler.next(businessError);
  }

  /// 记录错误信息
  void _logError(DioException err) {
    final method = err.requestOptions.method;
    final url = err.requestOptions.uri.toString();
    final statusCode = err.response?.statusCode;
    
    print('❌ Network Error: $method $url');
    print('   Status Code: $statusCode');
    print('   Error Type: ${err.type}');
    print('   Message: ${err.message}');
    
    if (err.response?.data != null) {
      print('   Response Data: ${err.response!.data}');
    }
  }

  /// 将DioException映射为业务错误
  Failure _mapDioExceptionToFailure(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
        return const NetworkFailure(
          message: '连接超时，请检查网络设置',
          code: 'CONNECTION_TIMEOUT',
        );

      case DioExceptionType.sendTimeout:
        return const NetworkFailure(
          message: '发送请求超时，请稍后重试',
          code: 'SEND_TIMEOUT',
        );

      case DioExceptionType.receiveTimeout:
        return const NetworkFailure(
          message: '接收响应超时，请稍后重试',
          code: 'RECEIVE_TIMEOUT',
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(exception.response!);

      case DioExceptionType.cancel:
        return const NetworkFailure(
          message: '请求已取消',
          code: 'REQUEST_CANCELLED',
        );

      case DioExceptionType.connectionError:
        return _handleConnectionError(exception);

      case DioExceptionType.badCertificate:
        return const NetworkFailure(
          message: 'SSL证书验证失败',
          code: 'BAD_CERTIFICATE',
        );

      case DioExceptionType.unknown:
      default:
        return _handleUnknownError(exception);
    }
  }

  /// 处理HTTP响应错误
  Failure _handleBadResponse(Response response) {
    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    String message = '请求失败';
    String code = 'HTTP_$statusCode';
    Map<String, dynamic>? details;

    // 尝试从响应中提取错误信息
    if (data is Map<String, dynamic>) {
      message = data['message'] ?? data['error'] ?? data['msg'] ?? message;
      code = data['code'] ?? data['error_code'] ?? code;
      details = data;
    } else if (data is String) {
      message = data.isNotEmpty ? data : message;
    }

    switch (statusCode) {
      case 400:
        return ValidationFailure(
          message: message.isNotEmpty ? message : '请求参数错误',
          code: code,
          details: details,
        );

      case 401:
        return AuthFailure(
          message: message.isNotEmpty ? message : '认证失败，请重新登录',
          code: code,
        );

      case 403:
        return AuthFailure(
          message: message.isNotEmpty ? message : '权限不足，无法访问该资源',
          code: code,
        );

      case 404:
        return ServerFailure(
          message: message.isNotEmpty ? message : '请求的资源不存在',
          code: code,
        );

      case 409:
        return ValidationFailure(
          message: message.isNotEmpty ? message : '数据冲突，请刷新后重试',
          code: code,
          details: details,
        );

      case 422:
        return ValidationFailure(
          message: message.isNotEmpty ? message : '数据验证失败',
          code: code,
          details: details,
        );

      case 429:
        return NetworkFailure(
          message: message.isNotEmpty ? message : '请求过于频繁，请稍后再试',
          code: code,
        );

      case 500:
        return ServerFailure(
          message: message.isNotEmpty ? message : '服务器内部错误',
          code: code,
        );

      case 502:
        return ServerFailure(
          message: message.isNotEmpty ? message : '网关错误，请稍后重试',
          code: code,
        );

      case 503:
        return ServerFailure(
          message: message.isNotEmpty ? message : '服务暂时不可用，请稍后重试',
          code: code,
        );

      case 504:
        return ServerFailure(
          message: message.isNotEmpty ? message : '网关超时，请稍后重试',
          code: code,
        );

      default:
        return ServerFailure(
          message: message.isNotEmpty ? message : '服务器响应异常 ($statusCode)',
          code: code,
        );
    }
  }

  /// 处理连接错误
  Failure _handleConnectionError(DioException exception) {
    final error = exception.error;
    
    if (error.toString().contains('SocketException')) {
      return const NetworkFailure(
        message: '网络连接失败，请检查网络设置',
        code: 'SOCKET_EXCEPTION',
      );
    }
    
    if (error.toString().contains('HandshakeException')) {
      return const NetworkFailure(
        message: 'SSL握手失败，请检查网络安全设置',
        code: 'HANDSHAKE_EXCEPTION',
      );
    }
    
    return NetworkFailure(
      message: '网络连接错误: ${exception.message}',
      code: 'CONNECTION_ERROR',
    );
  }

  /// 处理未知错误
  Failure _handleUnknownError(DioException exception) {
    final error = exception.error;
    
    if (error is Failure) {
      return error;
    }
    
    return UnknownFailure(
      '未知网络错误: ${exception.message ?? error?.toString() ?? '未知错误'}',
    );
  }

  /// 检查是否为网络错误
  static bool isNetworkError(dynamic error) {
    if (error is DioException) {
      return error.type == DioExceptionType.connectionError ||
             error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.receiveTimeout ||
             error.type == DioExceptionType.sendTimeout;
    }
    return false;
  }

  /// 检查是否为服务器错误
  static bool isServerError(dynamic error) {
    if (error is DioException && error.response != null) {
      final statusCode = error.response!.statusCode ?? 0;
      return statusCode >= 500 && statusCode < 600;
    }
    return false;
  }

  /// 检查是否为客户端错误
  static bool isClientError(dynamic error) {
    if (error is DioException && error.response != null) {
      final statusCode = error.response!.statusCode ?? 0;
      return statusCode >= 400 && statusCode < 500;
    }
    return false;
  }
}
