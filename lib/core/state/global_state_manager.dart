import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'dart:async';
import '../database/database_manager.dart';

/// 全局状态管理器
/// 
/// 管理应用级别的全局状态，提供状态持久化和恢复功能
/// **功能依赖**: 需要启用 state_management 模块
/// **配置项**: FEATURE_STATE_MANAGEMENT
@singleton
class GlobalStateManager {
  GlobalStateManager(this._databaseManager);

  final DatabaseManager _databaseManager;
  
  static const String _globalStateKey = 'global_state';
  final Map<String, dynamic> _state = {};
  final StreamController<GlobalStateChange> _stateChangeController = 
      StreamController<GlobalStateChange>.broadcast();

  /// 状态变更流
  Stream<GlobalStateChange> get stateChanges => _stateChangeController.stream;

  /// 初始化全局状态
  Future<void> initialize() async {
    await _loadPersistedState();
  }

  /// 设置状态值
  Future<void> setState<T>(String key, T value, {bool persist = true}) async {
    final oldValue = _state[key];
    _state[key] = value;

    // 发送状态变更事件
    _stateChangeController.add(GlobalStateChange(
      key: key,
      oldValue: oldValue,
      newValue: value,
    ));

    // 持久化状态
    if (persist) {
      await _persistState();
    }
  }

  /// 获取状态值
  T? getState<T>(String key) {
    return _state[key] as T?;
  }

  /// 获取状态值，如果不存在则返回默认值
  T getStateOrDefault<T>(String key, T defaultValue) {
    return _state[key] as T? ?? defaultValue;
  }

  /// 检查状态是否存在
  bool hasState(String key) {
    return _state.containsKey(key);
  }

  /// 移除状态
  Future<void> removeState(String key, {bool persist = true}) async {
    final oldValue = _state.remove(key);
    
    if (oldValue != null) {
      _stateChangeController.add(GlobalStateChange(
        key: key,
        oldValue: oldValue,
        newValue: null,
      ));

      if (persist) {
        await _persistState();
      }
    }
  }

  /// 批量设置状态
  Future<void> setStates(Map<String, dynamic> states, {bool persist = true}) async {
    final changes = <GlobalStateChange>[];
    
    for (final entry in states.entries) {
      final key = entry.key;
      final value = entry.value;
      final oldValue = _state[key];
      
      _state[key] = value;
      changes.add(GlobalStateChange(
        key: key,
        oldValue: oldValue,
        newValue: value,
      ));
    }

    // 批量发送状态变更事件
    for (final change in changes) {
      _stateChangeController.add(change);
    }

    if (persist) {
      await _persistState();
    }
  }

  /// 清空所有状态
  Future<void> clearState({bool persist = true}) async {
    final oldState = Map<String, dynamic>.from(_state);
    _state.clear();

    // 发送清空事件
    for (final entry in oldState.entries) {
      _stateChangeController.add(GlobalStateChange(
        key: entry.key,
        oldValue: entry.value,
        newValue: null,
      ));
    }

    if (persist) {
      await _persistState();
    }
  }

  /// 获取所有状态
  Map<String, dynamic> getAllStates() {
    return Map<String, dynamic>.from(_state);
  }

  /// 监听特定键的状态变更
  Stream<T?> watchState<T>(String key) {
    return _stateChangeController.stream
        .where((change) => change.key == key)
        .map((change) => change.newValue as T?);
  }

  /// 监听多个键的状态变更
  Stream<Map<String, dynamic>> watchStates(List<String> keys) {
    return _stateChangeController.stream
        .where((change) => keys.contains(change.key))
        .map((_) => Map<String, dynamic>.fromEntries(
          keys.map((key) => MapEntry(key, _state[key])),
        ));
  }

  /// 加载持久化状态
  Future<void> _loadPersistedState() async {
    try {
      final settingsBox = _databaseManager.settingsBox;
      final persistedState = settingsBox.get(_globalStateKey) as Map<dynamic, dynamic>?;
      
      if (persistedState != null) {
        _state.clear();
        _state.addAll(Map<String, dynamic>.from(persistedState));
      }
    } catch (e) {
      print('Failed to load persisted global state: $e');
    }
  }

  /// 持久化状态
  Future<void> _persistState() async {
    try {
      final settingsBox = _databaseManager.settingsBox;
      await settingsBox.put(_globalStateKey, _state);
    } catch (e) {
      print('Failed to persist global state: $e');
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await _stateChangeController.close();
  }
}

/// 全局状态变更事件
class GlobalStateChange {
  const GlobalStateChange({
    required this.key,
    required this.oldValue,
    required this.newValue,
  });

  final String key;
  final dynamic oldValue;
  final dynamic newValue;

  @override
  String toString() {
    return 'GlobalStateChange(key: $key, oldValue: $oldValue, newValue: $newValue)';
  }
}

/// 全局状态BLoC
@injectable
class GlobalStateBloc extends Cubit<GlobalState> {
  GlobalStateBloc(this._globalStateManager) : super(const GlobalState({})) {
    _initialize();
  }

  final GlobalStateManager _globalStateManager;
  StreamSubscription<GlobalStateChange>? _stateSubscription;

  void _initialize() {
    // 监听全局状态变更
    _stateSubscription = _globalStateManager.stateChanges.listen(
      (change) {
        final currentStates = Map<String, dynamic>.from(state.states);
        
        if (change.newValue == null) {
          currentStates.remove(change.key);
        } else {
          currentStates[change.key] = change.newValue;
        }
        
        emit(GlobalState(currentStates));
      },
    );

    // 初始化状态
    final initialStates = _globalStateManager.getAllStates();
    if (initialStates.isNotEmpty) {
      emit(GlobalState(initialStates));
    }
  }

  /// 设置全局状态
  Future<void> setGlobalState<T>(String key, T value, {bool persist = true}) async {
    await _globalStateManager.setState(key, value, persist: persist);
  }

  /// 获取全局状态
  T? getGlobalState<T>(String key) {
    return _globalStateManager.getState<T>(key);
  }

  /// 移除全局状态
  Future<void> removeGlobalState(String key, {bool persist = true}) async {
    await _globalStateManager.removeState(key, persist: persist);
  }

  /// 批量设置全局状态
  Future<void> setGlobalStates(Map<String, dynamic> states, {bool persist = true}) async {
    await _globalStateManager.setStates(states, persist: persist);
  }

  /// 清空全局状态
  Future<void> clearGlobalState({bool persist = true}) async {
    await _globalStateManager.clearState(persist: persist);
  }

  @override
  Future<void> close() async {
    await _stateSubscription?.cancel();
    return super.close();
  }
}

/// 全局状态
class GlobalState {
  const GlobalState(this.states);

  final Map<String, dynamic> states;

  /// 获取状态值
  T? get<T>(String key) {
    return states[key] as T?;
  }

  /// 获取状态值，如果不存在则返回默认值
  T getOrDefault<T>(String key, T defaultValue) {
    return states[key] as T? ?? defaultValue;
  }

  /// 检查状态是否存在
  bool has(String key) {
    return states.containsKey(key);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GlobalState && 
           other.states.length == states.length &&
           other.states.entries.every((entry) => 
               states[entry.key] == entry.value);
  }

  @override
  int get hashCode => states.hashCode;

  @override
  String toString() {
    return 'GlobalState(${states.length} states)';
  }
}

/// 全局状态扩展
extension GlobalStateExtension on BuildContext {
  /// 获取全局状态BLoC
  GlobalStateBloc get globalState => read<GlobalStateBloc>();

  /// 监听全局状态
  GlobalState watchGlobalState() => watch<GlobalStateBloc>().state;

  /// 读取全局状态
  GlobalState readGlobalState() => read<GlobalStateBloc>().state;

  /// 设置全局状态
  Future<void> setGlobalState<T>(String key, T value, {bool persist = true}) {
    return globalState.setGlobalState(key, value, persist: persist);
  }

  /// 获取全局状态值
  T? getGlobalState<T>(String key) {
    return globalState.getGlobalState<T>(key);
  }

  /// 获取全局状态值，如果不存在则返回默认值
  T getGlobalStateOrDefault<T>(String key, T defaultValue) {
    return globalState.getGlobalState<T>(key) ?? defaultValue;
  }
}
