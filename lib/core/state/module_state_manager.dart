import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'dart:async';

/// 模块状态管理器
/// 
/// 为每个功能模块提供独立的状态管理空间，实现状态隔离
/// **功能依赖**: 需要启用 state_management 模块
/// **配置项**: FEATURE_STATE_MANAGEMENT
@singleton
class ModuleStateManager {
  ModuleStateManager();

  final Map<String, ModuleStateContainer> _moduleStates = {};
  final StreamController<ModuleStateChange> _stateChangeController = 
      StreamController<ModuleStateChange>.broadcast();

  /// 模块状态变更流
  Stream<ModuleStateChange> get stateChanges => _stateChangeController.stream;

  /// 注册模块状态容器
  void registerModule(String moduleId, {Map<String, dynamic>? initialState}) {
    if (_moduleStates.containsKey(moduleId)) {
      throw StateError('Module $moduleId is already registered');
    }

    _moduleStates[moduleId] = ModuleStateContainer(
      moduleId: moduleId,
      initialState: initialState ?? {},
    );
  }

  /// 注销模块状态容器
  void unregisterModule(String moduleId) {
    final container = _moduleStates.remove(moduleId);
    container?.dispose();
  }

  /// 获取模块状态容器
  ModuleStateContainer? getModuleContainer(String moduleId) {
    return _moduleStates[moduleId];
  }

  /// 设置模块状态
  Future<void> setModuleState<T>(
    String moduleId,
    String key,
    T value,
  ) async {
    final container = _moduleStates[moduleId];
    if (container == null) {
      throw StateError('Module $moduleId is not registered');
    }

    final oldValue = container.getState(key);
    container.setState(key, value);

    _stateChangeController.add(ModuleStateChange(
      moduleId: moduleId,
      key: key,
      oldValue: oldValue,
      newValue: value,
    ));
  }

  /// 获取模块状态
  T? getModuleState<T>(String moduleId, String key) {
    final container = _moduleStates[moduleId];
    return container?.getState<T>(key);
  }

  /// 移除模块状态
  Future<void> removeModuleState(String moduleId, String key) async {
    final container = _moduleStates[moduleId];
    if (container == null) return;

    final oldValue = container.removeState(key);
    if (oldValue != null) {
      _stateChangeController.add(ModuleStateChange(
        moduleId: moduleId,
        key: key,
        oldValue: oldValue,
        newValue: null,
      ));
    }
  }

  /// 清空模块状态
  Future<void> clearModuleState(String moduleId) async {
    final container = _moduleStates[moduleId];
    if (container == null) return;

    final oldStates = container.getAllStates();
    container.clearState();

    for (final entry in oldStates.entries) {
      _stateChangeController.add(ModuleStateChange(
        moduleId: moduleId,
        key: entry.key,
        oldValue: entry.value,
        newValue: null,
      ));
    }
  }

  /// 监听模块状态变更
  Stream<T?> watchModuleState<T>(String moduleId, String key) {
    return _stateChangeController.stream
        .where((change) => change.moduleId == moduleId && change.key == key)
        .map((change) => change.newValue as T?);
  }

  /// 获取所有已注册的模块ID
  List<String> getRegisteredModules() {
    return _moduleStates.keys.toList();
  }

  /// 获取模块状态统计信息
  ModuleStateStats getStats() {
    final moduleStats = <String, int>{};
    int totalStates = 0;

    for (final entry in _moduleStates.entries) {
      final stateCount = entry.value.getStateCount();
      moduleStats[entry.key] = stateCount;
      totalStates += stateCount;
    }

    return ModuleStateStats(
      totalModules: _moduleStates.length,
      totalStates: totalStates,
      moduleStats: moduleStats,
    );
  }

  /// 释放资源
  Future<void> dispose() async {
    for (final container in _moduleStates.values) {
      container.dispose();
    }
    _moduleStates.clear();
    await _stateChangeController.close();
  }
}

/// 模块状态容器
class ModuleStateContainer {
  ModuleStateContainer({
    required this.moduleId,
    Map<String, dynamic>? initialState,
  }) : _state = Map<String, dynamic>.from(initialState ?? {});

  final String moduleId;
  final Map<String, dynamic> _state;
  final StreamController<StateChange> _stateChangeController = 
      StreamController<StateChange>.broadcast();

  /// 状态变更流
  Stream<StateChange> get stateChanges => _stateChangeController.stream;

  /// 设置状态
  void setState<T>(String key, T value) {
    final oldValue = _state[key];
    _state[key] = value;

    _stateChangeController.add(StateChange(
      key: key,
      oldValue: oldValue,
      newValue: value,
    ));
  }

  /// 获取状态
  T? getState<T>(String key) {
    return _state[key] as T?;
  }

  /// 获取状态，如果不存在则返回默认值
  T getStateOrDefault<T>(String key, T defaultValue) {
    return _state[key] as T? ?? defaultValue;
  }

  /// 检查状态是否存在
  bool hasState(String key) {
    return _state.containsKey(key);
  }

  /// 移除状态
  dynamic removeState(String key) {
    final oldValue = _state.remove(key);
    if (oldValue != null) {
      _stateChangeController.add(StateChange(
        key: key,
        oldValue: oldValue,
        newValue: null,
      ));
    }
    return oldValue;
  }

  /// 批量设置状态
  void setStates(Map<String, dynamic> states) {
    final changes = <StateChange>[];
    
    for (final entry in states.entries) {
      final key = entry.key;
      final value = entry.value;
      final oldValue = _state[key];
      
      _state[key] = value;
      changes.add(StateChange(
        key: key,
        oldValue: oldValue,
        newValue: value,
      ));
    }

    for (final change in changes) {
      _stateChangeController.add(change);
    }
  }

  /// 清空状态
  void clearState() {
    final oldState = Map<String, dynamic>.from(_state);
    _state.clear();

    for (final entry in oldState.entries) {
      _stateChangeController.add(StateChange(
        key: entry.key,
        oldValue: entry.value,
        newValue: null,
      ));
    }
  }

  /// 获取所有状态
  Map<String, dynamic> getAllStates() {
    return Map<String, dynamic>.from(_state);
  }

  /// 获取状态数量
  int getStateCount() {
    return _state.length;
  }

  /// 监听特定键的状态变更
  Stream<T?> watchState<T>(String key) {
    return _stateChangeController.stream
        .where((change) => change.key == key)
        .map((change) => change.newValue as T?);
  }

  /// 释放资源
  void dispose() {
    _stateChangeController.close();
  }
}

/// 模块状态变更事件
class ModuleStateChange {
  const ModuleStateChange({
    required this.moduleId,
    required this.key,
    required this.oldValue,
    required this.newValue,
  });

  final String moduleId;
  final String key;
  final dynamic oldValue;
  final dynamic newValue;

  @override
  String toString() {
    return 'ModuleStateChange(module: $moduleId, key: $key, oldValue: $oldValue, newValue: $newValue)';
  }
}

/// 状态变更事件
class StateChange {
  const StateChange({
    required this.key,
    required this.oldValue,
    required this.newValue,
  });

  final String key;
  final dynamic oldValue;
  final dynamic newValue;

  @override
  String toString() {
    return 'StateChange(key: $key, oldValue: $oldValue, newValue: $newValue)';
  }
}

/// 模块状态统计信息
class ModuleStateStats {
  const ModuleStateStats({
    required this.totalModules,
    required this.totalStates,
    required this.moduleStats,
  });

  final int totalModules;
  final int totalStates;
  final Map<String, int> moduleStats;

  @override
  String toString() {
    return 'ModuleStateStats(modules: $totalModules, states: $totalStates, details: $moduleStats)';
  }
}

/// 模块状态BLoC基类
abstract class ModuleStateBloc<State> extends Cubit<State> {
  ModuleStateBloc(
    this.moduleId,
    this._moduleStateManager,
    State initialState,
  ) : super(initialState) {
    _initialize();
  }

  final String moduleId;
  final ModuleStateManager _moduleStateManager;
  StreamSubscription<ModuleStateChange>? _stateSubscription;

  void _initialize() {
    // 注册模块
    _moduleStateManager.registerModule(moduleId);

    // 监听模块状态变更
    _stateSubscription = _moduleStateManager.stateChanges
        .where((change) => change.moduleId == moduleId)
        .listen(onModuleStateChanged);
  }

  /// 处理模块状态变更
  void onModuleStateChanged(ModuleStateChange change) {
    // 子类可以重写此方法来处理状态变更
  }

  /// 设置模块状态
  Future<void> setModuleState<T>(String key, T value) async {
    await _moduleStateManager.setModuleState(moduleId, key, value);
  }

  /// 获取模块状态
  T? getModuleState<T>(String key) {
    return _moduleStateManager.getModuleState<T>(moduleId, key);
  }

  /// 移除模块状态
  Future<void> removeModuleState(String key) async {
    await _moduleStateManager.removeModuleState(moduleId, key);
  }

  /// 监听模块状态
  Stream<T?> watchModuleState<T>(String key) {
    return _moduleStateManager.watchModuleState<T>(moduleId, key);
  }

  @override
  Future<void> close() async {
    await _stateSubscription?.cancel();
    _moduleStateManager.unregisterModule(moduleId);
    return super.close();
  }
}

/// 模块状态混入
mixin ModuleStateMixin<T extends StatefulWidget> on State<T> {
  late final ModuleStateContainer _moduleContainer;
  String get moduleId;

  @override
  void initState() {
    super.initState();
    _moduleContainer = ModuleStateContainer(moduleId: moduleId);
  }

  /// 设置模块状态
  void setModuleState<V>(String key, V value) {
    _moduleContainer.setState(key, value);
  }

  /// 获取模块状态
  V? getModuleState<V>(String key) {
    return _moduleContainer.getState<V>(key);
  }

  /// 监听模块状态
  Stream<V?> watchModuleState<V>(String key) {
    return _moduleContainer.watchState<V>(key);
  }

  @override
  void dispose() {
    _moduleContainer.dispose();
    super.dispose();
  }
}
