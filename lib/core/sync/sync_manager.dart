import 'package:injectable/injectable.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import '../database/database_manager.dart';
import '../network/dio_client.dart';
import '../errors/failures.dart';

/// 数据同步管理器
/// 
/// 提供本地数据与服务器数据的双向同步功能
/// **功能依赖**: 需要启用 sync 模块
/// **配置项**: FEATURE_SYNC
@singleton
class SyncManager {
  SyncManager(
    this._databaseManager,
    this._dioClient,
    this._connectivity,
  );

  final DatabaseManager _databaseManager;
  final DioClient _dioClient;
  final Connectivity _connectivity;

  static const String _syncMetadataKey = 'sync_metadata';
  static const String _pendingSyncKey = 'pending_sync';
  
  final StreamController<SyncEvent> _syncEventController = 
      StreamController<SyncEvent>.broadcast();
  
  Timer? _periodicSyncTimer;
  bool _isSyncing = false;
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  /// 同步事件流
  Stream<SyncEvent> get syncEvents => _syncEventController.stream;

  /// 是否正在同步
  bool get isSyncing => _isSyncing;

  /// 初始化同步管理器
  Future<void> initialize() async {
    // 监听网络连接状态
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
    );

    // 启动定期同步
    _startPeriodicSync();

    // 检查是否有待同步的数据
    await _processPendingSync();
  }

  /// 手动触发同步
  Future<SyncResult> sync({
    List<String>? collections,
    SyncDirection direction = SyncDirection.bidirectional,
    bool force = false,
  }) async {
    if (_isSyncing && !force) {
      return SyncResult.failure('同步正在进行中');
    }

    _isSyncing = true;
    _syncEventController.add(const SyncStarted());

    try {
      final result = await _performSync(collections, direction);
      _syncEventController.add(SyncCompleted(result));
      return result;
    } catch (e) {
      final error = 'Sync failed: $e';
      _syncEventController.add(SyncFailed(error));
      return SyncResult.failure(error);
    } finally {
      _isSyncing = false;
    }
  }

  /// 添加待同步数据
  Future<void> addPendingSync(SyncItem item) async {
    final pendingBox = _databaseManager.defaultBox;
    final pendingItems = _getPendingSyncItems();
    
    // 检查是否已存在相同的待同步项
    final existingIndex = pendingItems.indexWhere(
      (existing) => existing.collection == item.collection && 
                   existing.id == item.id,
    );

    if (existingIndex >= 0) {
      // 更新现有项
      pendingItems[existingIndex] = item.copyWith(
        timestamp: DateTime.now(),
      );
    } else {
      // 添加新项
      pendingItems.add(item);
    }

    await pendingBox.put(_pendingSyncKey, 
        pendingItems.map((item) => item.toJson()).toList());
  }

  /// 获取同步状态
  SyncStatus getSyncStatus() {
    final metadata = _getSyncMetadata();
    final pendingItems = _getPendingSyncItems();
    
    return SyncStatus(
      lastSyncTime: metadata['last_sync_time'] != null 
          ? DateTime.parse(metadata['last_sync_time'])
          : null,
      pendingItemsCount: pendingItems.length,
      isSyncing: _isSyncing,
      lastSyncResult: metadata['last_sync_result'],
    );
  }

  /// 清除同步数据
  Future<void> clearSyncData() async {
    final defaultBox = _databaseManager.defaultBox;
    await Future.wait([
      defaultBox.delete(_syncMetadataKey),
      defaultBox.delete(_pendingSyncKey),
    ]);
  }

  /// 执行同步操作
  Future<SyncResult> _performSync(
    List<String>? collections,
    SyncDirection direction,
  ) async {
    final startTime = DateTime.now();
    int uploadedCount = 0;
    int downloadedCount = 0;
    final errors = <String>[];

    try {
      // 检查网络连接
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        throw Exception('No network connection');
      }

      // 上传本地更改
      if (direction == SyncDirection.upload || 
          direction == SyncDirection.bidirectional) {
        final uploadResult = await _uploadLocalChanges(collections);
        uploadedCount = uploadResult.successCount;
        errors.addAll(uploadResult.errors);
      }

      // 下载服务器更改
      if (direction == SyncDirection.download || 
          direction == SyncDirection.bidirectional) {
        final downloadResult = await _downloadServerChanges(collections);
        downloadedCount = downloadResult.successCount;
        errors.addAll(downloadResult.errors);
      }

      // 更新同步元数据
      await _updateSyncMetadata(startTime, uploadedCount + downloadedCount, errors);

      return SyncResult.success(
        uploadedCount: uploadedCount,
        downloadedCount: downloadedCount,
        errors: errors,
      );
    } catch (e) {
      errors.add(e.toString());
      await _updateSyncMetadata(startTime, uploadedCount + downloadedCount, errors);
      return SyncResult.failure(e.toString());
    }
  }

  /// 上传本地更改
  Future<SyncOperationResult> _uploadLocalChanges(List<String>? collections) async {
    final pendingItems = _getPendingSyncItems();
    final filteredItems = collections != null 
        ? pendingItems.where((item) => collections.contains(item.collection)).toList()
        : pendingItems;

    int successCount = 0;
    final errors = <String>[];

    for (final item in filteredItems) {
      try {
        await _uploadSyncItem(item);
        await _removePendingSyncItem(item);
        successCount++;
      } catch (e) {
        errors.add('Failed to upload ${item.collection}:${item.id}: $e');
      }
    }

    return SyncOperationResult(successCount, errors);
  }

  /// 下载服务器更改
  Future<SyncOperationResult> _downloadServerChanges(List<String>? collections) async {
    int successCount = 0;
    final errors = <String>[];

    try {
      final metadata = _getSyncMetadata();
      final lastSyncTime = metadata['last_sync_time'] != null 
          ? DateTime.parse(metadata['last_sync_time'])
          : null;

      final response = await _dioClient.dio.get('/sync/changes', queryParameters: {
        'since': lastSyncTime?.toIso8601String(),
        'collections': collections?.join(','),
      });

      final changes = response.data['changes'] as List;
      
      for (final change in changes) {
        try {
          await _applyServerChange(change);
          successCount++;
        } catch (e) {
          errors.add('Failed to apply change: $e');
        }
      }
    } catch (e) {
      errors.add('Failed to download changes: $e');
    }

    return SyncOperationResult(successCount, errors);
  }

  /// 上传同步项
  Future<void> _uploadSyncItem(SyncItem item) async {
    final endpoint = '/sync/${item.collection}';
    
    switch (item.operation) {
      case SyncOperation.create:
        await _dioClient.dio.post(endpoint, data: item.data);
        break;
      case SyncOperation.update:
        await _dioClient.dio.put('$endpoint/${item.id}', data: item.data);
        break;
      case SyncOperation.delete:
        await _dioClient.dio.delete('$endpoint/${item.id}');
        break;
    }
  }

  /// 应用服务器更改
  Future<void> _applyServerChange(Map<String, dynamic> change) async {
    final collection = change['collection'] as String;
    final operation = SyncOperation.values.firstWhere(
      (op) => op.name == change['operation'],
    );
    final id = change['id'] as String;
    final data = change['data'] as Map<String, dynamic>?;

    // 这里应该根据具体的数据模型来处理
    // 暂时简化实现
    final box = await _databaseManager.openBox(collection);
    
    switch (operation) {
      case SyncOperation.create:
      case SyncOperation.update:
        if (data != null) {
          await box.put(id, data);
        }
        break;
      case SyncOperation.delete:
        await box.delete(id);
        break;
    }
  }

  /// 获取待同步项列表
  List<SyncItem> _getPendingSyncItems() {
    final defaultBox = _databaseManager.defaultBox;
    final data = defaultBox.get(_pendingSyncKey) as List?;
    
    if (data == null) return [];
    
    return data.map((item) => SyncItem.fromJson(item as Map<String, dynamic>)).toList();
  }

  /// 移除待同步项
  Future<void> _removePendingSyncItem(SyncItem item) async {
    final pendingItems = _getPendingSyncItems();
    pendingItems.removeWhere(
      (existing) => existing.collection == item.collection && 
                   existing.id == item.id,
    );
    
    final defaultBox = _databaseManager.defaultBox;
    await defaultBox.put(_pendingSyncKey, 
        pendingItems.map((item) => item.toJson()).toList());
  }

  /// 获取同步元数据
  Map<String, dynamic> _getSyncMetadata() {
    final defaultBox = _databaseManager.defaultBox;
    return Map<String, dynamic>.from(
      defaultBox.get(_syncMetadataKey, defaultValue: <String, dynamic>{}),
    );
  }

  /// 更新同步元数据
  Future<void> _updateSyncMetadata(
    DateTime syncTime,
    int itemCount,
    List<String> errors,
  ) async {
    final metadata = {
      'last_sync_time': syncTime.toIso8601String(),
      'last_sync_item_count': itemCount,
      'last_sync_result': errors.isEmpty ? 'success' : 'partial_failure',
      'last_sync_errors': errors,
    };

    final defaultBox = _databaseManager.defaultBox;
    await defaultBox.put(_syncMetadataKey, metadata);
  }

  /// 处理待同步数据
  Future<void> _processPendingSync() async {
    final pendingItems = _getPendingSyncItems();
    if (pendingItems.isNotEmpty) {
      // 在后台自动同步待处理的数据
      unawaited(sync());
    }
  }

  /// 启动定期同步
  void _startPeriodicSync() {
    _periodicSyncTimer = Timer.periodic(
      const Duration(minutes: 15), // 每15分钟同步一次
      (_) => _processPendingSync(),
    );
  }

  /// 网络连接状态变化处理
  void _onConnectivityChanged(ConnectivityResult result) {
    if (result != ConnectivityResult.none) {
      // 网络恢复时触发同步
      _processPendingSync();
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    _periodicSyncTimer?.cancel();
    await _connectivitySubscription.cancel();
    await _syncEventController.close();
  }
}

/// 同步项
class SyncItem {
  const SyncItem({
    required this.collection,
    required this.id,
    required this.operation,
    required this.data,
    required this.timestamp,
  });

  final String collection;
  final String id;
  final SyncOperation operation;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  SyncItem copyWith({
    String? collection,
    String? id,
    SyncOperation? operation,
    Map<String, dynamic>? data,
    DateTime? timestamp,
  }) {
    return SyncItem(
      collection: collection ?? this.collection,
      id: id ?? this.id,
      operation: operation ?? this.operation,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'collection': collection,
      'id': id,
      'operation': operation.name,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory SyncItem.fromJson(Map<String, dynamic> json) {
    return SyncItem(
      collection: json['collection'] as String,
      id: json['id'] as String,
      operation: SyncOperation.values.firstWhere(
        (op) => op.name == json['operation'],
      ),
      data: json['data'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// 同步操作类型
enum SyncOperation { create, update, delete }

/// 同步方向
enum SyncDirection { upload, download, bidirectional }

/// 同步事件
abstract class SyncEvent {
  const SyncEvent();
}

class SyncStarted extends SyncEvent {
  const SyncStarted();
}

class SyncCompleted extends SyncEvent {
  const SyncCompleted(this.result);
  final SyncResult result;
}

class SyncFailed extends SyncEvent {
  const SyncFailed(this.error);
  final String error;
}

/// 同步结果
class SyncResult {
  const SyncResult({
    required this.success,
    this.uploadedCount = 0,
    this.downloadedCount = 0,
    this.errors = const [],
  });

  final bool success;
  final int uploadedCount;
  final int downloadedCount;
  final List<String> errors;

  factory SyncResult.success({
    int uploadedCount = 0,
    int downloadedCount = 0,
    List<String> errors = const [],
  }) {
    return SyncResult(
      success: true,
      uploadedCount: uploadedCount,
      downloadedCount: downloadedCount,
      errors: errors,
    );
  }

  factory SyncResult.failure(String error) {
    return SyncResult(
      success: false,
      errors: [error],
    );
  }
}

/// 同步操作结果
class SyncOperationResult {
  const SyncOperationResult(this.successCount, this.errors);
  final int successCount;
  final List<String> errors;
}

/// 同步状态
class SyncStatus {
  const SyncStatus({
    this.lastSyncTime,
    required this.pendingItemsCount,
    required this.isSyncing,
    this.lastSyncResult,
  });

  final DateTime? lastSyncTime;
  final int pendingItemsCount;
  final bool isSyncing;
  final String? lastSyncResult;
}
