import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';
import '../entities/auth_token.dart';

/// 用户注册参数
class UserRegistration {
  const UserRegistration({
    required this.email,
    required this.password,
    required this.name,
    this.phone,
    this.acceptTerms = false,
    this.metadata = const {},
  });

  final String email;
  final String password;
  final String name;
  final String? phone;
  final bool acceptTerms;
  final Map<String, dynamic> metadata;
}

/// 密码重置参数
class PasswordResetRequest {
  const PasswordResetRequest({
    required this.token,
    required this.newPassword,
    required this.confirmPassword,
  });

  final String token;
  final String newPassword;
  final String confirmPassword;
}

/// 密码修改参数
class PasswordChangeRequest {
  const PasswordChangeRequest({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  final String currentPassword;
  final String newPassword;
  final String confirmPassword;
}

/// 认证仓库接口
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
abstract class IAuthRepository {
  /// 用户登录
  /// 
  /// [email] 用户邮箱
  /// [password] 用户密码
  /// [rememberMe] 是否记住登录状态
  /// 
  /// 返回认证结果或失败信息
  Future<Either<Failure, AuthResult>> login(
    String email, 
    String password, {
    bool rememberMe = false,
  });

  /// 用户注册
  /// 
  /// [registration] 注册信息
  /// 
  /// 返回认证结果或失败信息
  Future<Either<Failure, AuthResult>> register(UserRegistration registration);

  /// 用户登出
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> logout();

  /// 刷新访问令牌
  /// 
  /// [refreshToken] 刷新令牌
  /// 
  /// 返回新的认证令牌或失败信息
  Future<Either<Failure, AuthToken>> refreshToken(String refreshToken);

  /// 获取当前用户信息
  /// 
  /// 返回用户信息或失败信息
  Future<Either<Failure, User?>> getCurrentUser();

  /// 检查认证状态
  /// 
  /// 返回是否已认证
  Future<Either<Failure, bool>> isAuthenticated();

  /// 忘记密码
  /// 
  /// [email] 用户邮箱
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> forgotPassword(String email);

  /// 重置密码
  /// 
  /// [request] 密码重置请求
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> resetPassword(PasswordResetRequest request);

  /// 修改密码
  /// 
  /// [request] 密码修改请求
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> changePassword(PasswordChangeRequest request);

  /// 验证邮箱
  /// 
  /// [token] 验证令牌
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> verifyEmail(String token);

  /// 重新发送邮箱验证
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> resendEmailVerification();

  /// 验证手机号
  /// 
  /// [phone] 手机号
  /// [code] 验证码
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> verifyPhone(String phone, String code);

  /// 发送手机验证码
  /// 
  /// [phone] 手机号
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> sendPhoneVerificationCode(String phone);

  /// 启用双因素认证
  /// 
  /// [secret] 双因素认证密钥
  /// [code] 验证码
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> enableTwoFactorAuth(String secret, String code);

  /// 禁用双因素认证
  /// 
  /// [code] 验证码
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> disableTwoFactorAuth(String code);

  /// 验证双因素认证码
  /// 
  /// [code] 验证码
  /// 
  /// 返回认证结果或失败信息
  Future<Either<Failure, AuthResult>> verifyTwoFactorAuth(String code);

  /// 获取双因素认证密钥
  /// 
  /// 返回密钥或失败信息
  Future<Either<Failure, String>> getTwoFactorAuthSecret();

  /// 社交登录
  /// 
  /// [provider] 社交平台提供商（google, facebook, apple等）
  /// [accessToken] 社交平台访问令牌
  /// 
  /// 返回认证结果或失败信息
  Future<Either<Failure, AuthResult>> socialLogin(
    String provider, 
    String accessToken,
  );

  /// 绑定社交账号
  /// 
  /// [provider] 社交平台提供商
  /// [accessToken] 社交平台访问令牌
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> bindSocialAccount(
    String provider, 
    String accessToken,
  );

  /// 解绑社交账号
  /// 
  /// [provider] 社交平台提供商
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> unbindSocialAccount(String provider);

  /// 删除账号
  /// 
  /// [password] 当前密码确认
  /// 
  /// 返回成功或失败信息
  Future<Either<Failure, void>> deleteAccount(String password);
}
