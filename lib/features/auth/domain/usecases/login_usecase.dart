import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/domain/use_case_base.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_token.dart';
import '../repositories/auth_repository.dart';

/// 登录用例参数
class LoginParams {
  const LoginParams({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  final String email;
  final String password;
  final bool rememberMe;
}

/// 登录用例
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@injectable
class LoginUseCase implements UseCaseBase<AuthResult, LoginParams> {
  const LoginUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Future<Either<Failure, AuthResult>> call(LoginParams params) async {
    // 验证输入参数
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      // 执行登录
      final result = await _repository.login(
        params.email,
        params.password,
        rememberMe: params.rememberMe,
      );

      return result.fold(
        (failure) => Left(failure),
        (authResult) {
          // 登录成功后的额外处理
          _logLoginSuccess(authResult.user.email);
          return Right(authResult);
        },
      );
    } catch (e) {
      return Left(UnknownFailure('登录过程中发生未知错误: $e'));
    }
  }

  /// 验证登录参数
  ValidationFailure? _validateParams(LoginParams params) {
    final errors = <String, List<String>>{};

    // 验证邮箱
    if (params.email.isEmpty) {
      errors['email'] = ['邮箱地址不能为空'];
    } else if (!_isValidEmail(params.email)) {
      errors['email'] = ['请输入有效的邮箱地址'];
    }

    // 验证密码
    if (params.password.isEmpty) {
      errors['password'] = ['密码不能为空'];
    } else if (params.password.length < 6) {
      errors['password'] = ['密码长度不能少于6位'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        message: '登录信息验证失败',
        details: errors,
      );
    }

    return null;
  }

  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// 记录登录成功日志
  void _logLoginSuccess(String email) {
    // 在实际应用中，这里可以记录到日志系统
    print('用户登录成功: $email');
  }
}

/// 登出用例
@injectable
class LogoutUseCase implements UseCaseBase<void, NoParams> {
  const LogoutUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    try {
      final result = await _repository.logout();
      
      return result.fold(
        (failure) => Left(failure),
        (success) {
          _logLogoutSuccess();
          return const Right(null);
        },
      );
    } catch (e) {
      return Left(UnknownFailure('登出过程中发生未知错误: $e'));
    }
  }

  /// 记录登出成功日志
  void _logLogoutSuccess() {
    print('用户登出成功');
  }
}

/// 获取当前用户用例
@injectable
class GetCurrentUserUseCase implements UseCaseBase<User?, NoParams> {
  const GetCurrentUserUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Future<Either<Failure, User?>> call(NoParams params) async {
    try {
      return await _repository.getCurrentUser();
    } catch (e) {
      return Left(UnknownFailure('获取用户信息时发生未知错误: $e'));
    }
  }
}

/// 检查认证状态用例
@injectable
class CheckAuthStatusUseCase implements UseCaseBase<bool, NoParams> {
  const CheckAuthStatusUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    try {
      return await _repository.isAuthenticated();
    } catch (e) {
      return Left(UnknownFailure('检查认证状态时发生未知错误: $e'));
    }
  }
}

/// 刷新令牌用例
@injectable
class RefreshTokenUseCase implements UseCaseBase<AuthToken, String> {
  const RefreshTokenUseCase(this._repository);

  final IAuthRepository _repository;

  @override
  Future<Either<Failure, AuthToken>> call(String refreshToken) async {
    if (refreshToken.isEmpty) {
      return const Left(ValidationFailure(
        message: '刷新令牌不能为空',
        details: {'refresh_token': ['刷新令牌不能为空']},
      ));
    }

    try {
      return await _repository.refreshToken(refreshToken);
    } catch (e) {
      return Left(UnknownFailure('刷新令牌时发生未知错误: $e'));
    }
  }
}
