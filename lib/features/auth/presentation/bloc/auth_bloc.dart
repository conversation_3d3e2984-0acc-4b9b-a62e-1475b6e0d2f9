import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/presentation/bloc_base.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/auth_token.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// 认证BLoC
/// 
/// **功能依赖**: 需要启用 authentication 模块
/// **配置项**: FEATURE_AUTHENTICATION
@injectable
class AuthBloc extends BlocBase<AuthEvent, AuthState> {
  AuthBloc(
    this._loginUseCase,
    this._logoutUseCase,
    this._registerUseCase,
    this._getCurrentUserUseCase,
    this._checkAuthStatusUseCase,
    this._refreshTokenUseCase,
    this._forgotPasswordUseCase,
    this._resetPasswordUseCase,
  ) : super(const AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthRegisterRequested>(_onRegisterRequested);
    on<AuthCheckRequested>(_onCheckRequested);
    on<AuthRefreshTokenRequested>(_onRefreshTokenRequested);
    on<AuthForgotPasswordRequested>(_onForgotPasswordRequested);
    on<AuthResetPasswordRequested>(_onResetPasswordRequested);
    on<AuthUserChanged>(_onUserChanged);
    on<AuthClearError>(_onClearError);
  }

  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final RegisterUseCase _registerUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;
  final CheckAuthStatusUseCase _checkAuthStatusUseCase;
  final RefreshTokenUseCase _refreshTokenUseCase;
  final ForgotPasswordUseCase _forgotPasswordUseCase;
  final ResetPasswordUseCase _resetPasswordUseCase;

  @override
  AuthState createErrorState(Failure failure) {
    return AuthError(failure.message);
  }

  /// 处理登录请求
  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    await executeAsyncOperation(
      _loginUseCase(LoginParams(
        email: event.email,
        password: event.password,
        rememberMe: event.rememberMe,
      )),
      (authResult) {
        safeEmit(AuthAuthenticated(
          user: authResult.user,
          token: authResult.token,
          isFirstLogin: authResult.isFirstLogin,
          requiresPasswordChange: authResult.requiresPasswordChange,
          requiresEmailVerification: authResult.requiresEmailVerification,
          requiresTwoFactorAuth: authResult.requiresTwoFactorAuth,
        ));
      },
      const AuthLoading(),
    );
  }

  /// 处理登出请求
  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    await executeAsyncOperation(
      _logoutUseCase(NoParams()),
      (_) => safeEmit(const AuthUnauthenticated()),
      const AuthLoading(),
    );
  }

  /// 处理注册请求
  Future<void> _onRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    await executeAsyncOperation(
      _registerUseCase(RegisterParams(
        email: event.email,
        password: event.password,
        confirmPassword: event.confirmPassword,
        name: event.name,
        phone: event.phone,
        acceptTerms: event.acceptTerms,
        metadata: event.metadata,
      )),
      (authResult) {
        safeEmit(AuthAuthenticated(
          user: authResult.user,
          token: authResult.token,
          isFirstLogin: authResult.isFirstLogin,
          requiresPasswordChange: authResult.requiresPasswordChange,
          requiresEmailVerification: authResult.requiresEmailVerification,
          requiresTwoFactorAuth: authResult.requiresTwoFactorAuth,
        ));
      },
      const AuthLoading(),
    );
  }

  /// 处理认证状态检查
  Future<void> _onCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    // 首先检查认证状态
    final authStatusResult = await _checkAuthStatusUseCase(NoParams());
    
    await authStatusResult.fold(
      (failure) async {
        safeEmit(const AuthUnauthenticated());
      },
      (isAuthenticated) async {
        if (isAuthenticated) {
          // 如果已认证，获取用户信息
          final userResult = await _getCurrentUserUseCase(NoParams());
          userResult.fold(
            (failure) => safeEmit(const AuthUnauthenticated()),
            (user) {
              if (user != null) {
                safeEmit(AuthAuthenticated(
                  user: user,
                  token: const AuthToken(
                    accessToken: '',
                    refreshToken: '',
                    tokenType: 'Bearer',
                    expiresIn: 3600,
                  ), // 这里应该从本地存储获取实际token
                ));
              } else {
                safeEmit(const AuthUnauthenticated());
              }
            },
          );
        } else {
          safeEmit(const AuthUnauthenticated());
        }
      },
    );
  }

  /// 处理刷新令牌请求
  Future<void> _onRefreshTokenRequested(
    AuthRefreshTokenRequested event,
    Emitter<AuthState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AuthAuthenticated) return;

    final result = await _refreshTokenUseCase(event.refreshToken);
    result.fold(
      (failure) => safeEmit(const AuthUnauthenticated()),
      (newToken) {
        safeEmit(currentState.copyWith(token: newToken));
      },
    );
  }

  /// 处理忘记密码请求
  Future<void> _onForgotPasswordRequested(
    AuthForgotPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _forgotPasswordUseCase(event.email);
    result.fold(
      (failure) => safeEmit(AuthError(failure.message)),
      (_) => safeEmit(const AuthPasswordResetEmailSent()),
    );
  }

  /// 处理重置密码请求
  Future<void> _onResetPasswordRequested(
    AuthResetPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _resetPasswordUseCase(event.request);
    result.fold(
      (failure) => safeEmit(AuthError(failure.message)),
      (_) => safeEmit(const AuthPasswordResetSuccess()),
    );
  }

  /// 处理用户信息变更
  Future<void> _onUserChanged(
    AuthUserChanged event,
    Emitter<AuthState> emit,
  ) async {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      safeEmit(currentState.copyWith(user: event.user));
    }
  }

  /// 处理清除错误
  Future<void> _onClearError(
    AuthClearError event,
    Emitter<AuthState> emit,
  ) async {
    final currentState = state;
    if (currentState is AuthError) {
      safeEmit(const AuthInitial());
    }
  }

  /// 获取当前用户
  User? get currentUser {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      return currentState.user;
    }
    return null;
  }

  /// 检查是否已认证
  bool get isAuthenticated {
    return state is AuthAuthenticated;
  }

  /// 检查是否需要额外操作
  bool get requiresAdditionalAction {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      return currentState.requiresPasswordChange ||
             currentState.requiresEmailVerification ||
             currentState.requiresTwoFactorAuth;
    }
    return false;
  }

  /// 检查用户是否有指定角色
  bool hasRole(String role) {
    return currentUser?.hasRole(role) ?? false;
  }

  /// 检查用户是否有指定权限
  bool hasPermission(String permission) {
    return currentUser?.hasPermission(permission) ?? false;
  }

  /// 检查用户是否有任一指定角色
  bool hasAnyRole(List<String> roles) {
    return currentUser?.hasAnyRole(roles) ?? false;
  }

  /// 检查用户是否有任一指定权限
  bool hasAnyPermission(List<String> permissions) {
    return currentUser?.hasAnyPermission(permissions) ?? false;
  }
}
